import 'package:flutter_test/flutter_test.dart';
import 'package:gp_stock_app/core/utils/trade_action_helper.dart';

void main() {
  group('TradeActionHelper', () {
    test('getActionLabel returns correct action labels', () {
      // Test all combinations
      expect(TradeActionHelper.getActionLabel(1, 1), 'openLong'); // 开多
      expect(TradeActionHelper.getActionLabel(1, 2), 'openShort'); // 开空
      expect(TradeActionHelper.getActionLabel(2, 1), 'closeLong'); // 平多
      expect(TradeActionHelper.getActionLabel(2, 2), 'closeShort'); // 平空
    });

    test('getActionLabel returns fallback for invalid combinations', () {
      expect(TradeActionHelper.getActionLabel(0, 0), '--');
      expect(TradeActionHelper.getActionLabel(3, 1), '--');
      expect(TradeActionHelper.getActionLabel(1, 3), '--');
    });

    test('getCombinedActionLabel returns correct combined labels', () {
      expect(TradeActionHelper.getCombinedActionLabel(1, 1), 'buyOpenLong'); // 买入开多
      expect(TradeActionHelper.getCombinedActionLabel(1, 2), 'buyOpenShort'); // 买入开空
      expect(TradeActionHelper.getCombinedActionLabel(2, 1), 'sellCloseLong'); // 卖出平多
      expect(TradeActionHelper.getCombinedActionLabel(2, 2), 'sellCloseShort'); // 卖出平空
    });

    test('getCombinedActionLabel returns default for invalid combinations', () {
      expect(TradeActionHelper.getCombinedActionLabel(0, 0), '--');
      expect(TradeActionHelper.getCombinedActionLabel(3, 1), '--');
      expect(TradeActionHelper.getCombinedActionLabel(1, 3), '--');
    });
  });
}
