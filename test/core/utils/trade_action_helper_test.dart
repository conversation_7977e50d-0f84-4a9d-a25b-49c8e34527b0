import 'package:flutter_test/flutter_test.dart';
import 'package:gp_stock_app/core/utils/trade_action_helper.dart';

void main() {
  group('TradeActionHelper', () {
    test('getActionLabel returns correct combined direction+action labels', () {
      // Test all combinations matching H5 implementation
      expect(TradeActionHelper.getActionLabel(1, 1), 'buyOpenLong'); // 买入开多
      expect(TradeActionHelper.getActionLabel(1, 2), 'buyOpenShort'); // 买入开空
      expect(TradeActionHelper.getActionLabel(2, 1), 'sellCloseLong'); // 卖出平多
      expect(TradeActionHelper.getActionLabel(2, 2), 'sellCloseShort'); // 卖出平空
    });

    test('getActionLabel returns fallback for invalid combinations', () {
      expect(TradeActionHelper.getActionLabel(0, 0), '--');
      expect(TradeActionHelper.getActionLabel(3, 1), '--');
      expect(TradeActionHelper.getActionLabel(1, 3), '--');
    });

    test('getActionOnlyLabel returns correct action-only labels for backward compatibility', () {
      expect(TradeActionHelper.getActionOnlyLabel(1, 1), 'openLong'); // 开多
      expect(TradeActionHelper.getActionOnlyLabel(1, 2), 'openShort'); // 开空
      expect(TradeActionHelper.getActionOnlyLabel(2, 1), 'closeLong'); // 平多
      expect(TradeActionHelper.getActionOnlyLabel(2, 2), 'closeShort'); // 平空
    });

    test('getActionOnlyLabel returns fallback for invalid combinations', () {
      expect(TradeActionHelper.getActionOnlyLabel(0, 0), '--');
      expect(TradeActionHelper.getActionOnlyLabel(3, 1), '--');
      expect(TradeActionHelper.getActionOnlyLabel(1, 3), '--');
    });
  });
}
