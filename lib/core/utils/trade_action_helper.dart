import 'package:easy_localization/easy_localization.dart';

class TradeActionHelper {
  // Combined direction + action labels (matching H5 implementation)
  static const Map<String, String> _combinedActionMap = {
    "1-1": "buyOpenLong", // 买入开多
    "1-2": "buyOpenShort", // 买入开空
    "2-1": "sellCloseLong", // 卖出平多
    "2-2": "sellCloseShort", // 卖出平空
  };

  // Legacy action-only labels (for backward compatibility)
  static const Map<String, String> _actionOnlyMap = {
    "1-1": "openLong", // 开多
    "1-2": "openShort", // 开空
    "2-1": "closeLong", // 平多
    "2-2": "closeShort", // 平空
  };

  /// Returns combined direction + action label (e.g., "买入开多", "卖出平多")
  /// This matches the H5 implementation behavior
  static String getActionLabel(int direction, int tradeType) {
    final key = "$direction-$tradeType";
    final translationKey = _combinedActionMap[key];
    return translationKey?.tr() ?? "--";
  }

  /// Returns action-only label (e.g., "开多", "平多") for backward compatibility
  static String getActionOnlyLabel(int direction, int tradeType) {
    final key = "$direction-$tradeType";
    final translationKey = _actionOnlyMap[key];
    return translationKey?.tr() ?? "--";
  }
}
