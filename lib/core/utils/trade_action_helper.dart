import 'package:easy_localization/easy_localization.dart';

class TradeActionHelper {
  static const Map<String, String> _actionMap = {
    "1-1": "openLong",     // 开多
    "1-2": "openShort",    // 开空  
    "2-1": "closeLong",    // 平多
    "2-2": "closeShort",   // 平空
  };

  static String getActionLabel(int direction, int tradeType) {
    final key = "$direction-$tradeType";
    final translationKey = _actionMap[key];
    return translationKey?.tr() ?? "--";
  }
}