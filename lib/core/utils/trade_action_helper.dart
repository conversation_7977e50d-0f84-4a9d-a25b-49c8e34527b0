import 'package:easy_localization/easy_localization.dart';

class TradeActionHelper {
  // Combined direction + action labels (for trade bottomsheet orders)
  static const Map<String, String> _combinedActionMap = {
    "1-1": "buyOpenLong", // 买入开多
    "1-2": "buyOpenShort", // 买入开空
    "2-1": "sellCloseLong", // 卖出平多
    "2-2": "sellCloseShort", // 卖出平空
  };

  // Action-only labels (for order detail screen)
  static const Map<String, String> _actionMap = {
    "1-1": "openLong", // 开多
    "1-2": "openShort", // 开空
    "2-1": "closeLong", // 平多
    "2-2": "closeShort", // 平空
  };

  /// Returns action label (e.g., "开多", "平多", "开空", "平空")
  /// Used for order detail screen where direction and action are shown separately
  static String getActionLabel(int direction, int tradeType) {
    final key = "$direction-$tradeType";
    final translationKey = _actionMap[key];
    return translationKey?.tr() ?? "--";
  }

  /// Returns combined direction + action label (e.g., "买入开多", "卖出平多")
  /// Used for trade bottomsheet orders where entrust direction shows the complete label
  static String getCombinedActionLabel(int direction, int tradeType) {
    final key = "$direction-$tradeType";
    final translationKey = _combinedActionMap[key];
    return translationKey?.tr() ?? "--";
  }
}
